import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import * as SunCalc from 'suncalc';

// Interface pour un nuage
interface Cloud {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  opacity: number;
  type: 'small' | 'medium' | 'large';
}

// Interface pour un oiseau
interface Bird {
  id: number;
  x: number;
  y: number;
  speed: number;
  direction: 'left' | 'right';
  wingPhase: number;
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cloudsRef = useRef<Cloud[]>([]);
  const birdsRef = useRef<Bird[]>([]);
  const animationsRef = useRef<gsap.core.Timeline[]>([]);

  // État pour la visibilité des éléments diurnes
  const [diurnalOpacity, setDiurnalOpacity] = useState(0);

  // État pour la géolocalisation (même système que AstronomicalLayer)
  const [userLocation, setUserLocation] = useState<{lat: number, lon: number}>({
    lat: 48.8566, // Paris par défaut
    lon: 2.3522
  });
  const [locationReady, setLocationReady] = useState(false);

  // Générer les nuages
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 3; // 2-3 nuages comme demandé

    for (let i = 0; i < cloudCount; i++) {
      const types: ('small' | 'medium' | 'large')[] = ['small', 'medium', 'large'];
      clouds.push({
        id: i,
        x: Math.random() * 120 - 20, // Commencer hors écran à gauche
        y: Math.random() * 30 + 10, // Dans les 30% supérieurs de l'écran
        size: Math.random() * 0.5 + 0.5, // 0.5x à 1x
        speed: Math.random() * 0.3 + 0.1, // Vitesse lente pour les nuages
        opacity: Math.random() * 0.4 + 0.6, // 0.6 à 1.0
        type: types[Math.floor(Math.random() * types.length)]
      });
    }

    return clouds;
  };

  // Générer les oiseaux
  const generateBirds = (): Bird[] => {
    const birds: Bird[] = [];
    const birdCount = 2; // 2 oiseaux

    for (let i = 0; i < birdCount; i++) {
      birds.push({
        id: i,
        x: Math.random() * 120 - 20, // Commencer hors écran
        y: Math.random() * 40 + 15, // Dans les 40% supérieurs
        speed: Math.random() * 0.8 + 0.5, // Plus rapide que les nuages
        direction: Math.random() > 0.5 ? 'right' : 'left',
        wingPhase: Math.random() * Math.PI * 2
      });
    }

    return birds;
  };

  // Calculer l'opacité des éléments diurnes selon l'heure
  const calculateDiurnalOpacity = (currentTime: Date): number => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Période de jour : éléments diurnes visibles
    if (currentHour >= sunrise + 0.5 && currentHour <= sunset - 0.5) {
      return 1.0;
    }

    // Transition progressive après le lever du soleil
    if (currentHour >= sunrise && currentHour < sunrise + 0.5) {
      const progress = (currentHour - sunrise) / 0.5;
      return progress;
    }

    // Transition progressive avant le coucher du soleil
    if (currentHour > sunset - 0.5 && currentHour <= sunset) {
      const progress = (currentHour - (sunset - 0.5)) / 0.5;
      return 1.0 - progress;
    }

    // Nuit : pas d'éléments diurnes
    return 0;
  };

  // Créer l'animation d'un nuage
  const createCloudAnimation = (cloudElement: HTMLElement, cloud: Cloud) => {
    const timeline = gsap.timeline({
      repeat: -1,
      force3D: true,
      willChange: "transform"
    });

    // Animation de déplacement horizontal
    timeline.fromTo(cloudElement, 
      { x: '-20vw' },
      { 
        x: '120vw',
        duration: 60 / cloud.speed, // Plus lent = plus de temps
        ease: "none"
      }
    );

    // Animation de flottement vertical
    gsap.to(cloudElement, {
      y: '+=10',
      duration: 8 + Math.random() * 4,
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut"
    });

    return timeline;
  };

  // Créer l'animation d'un oiseau
  const createBirdAnimation = (birdElement: HTMLElement, bird: Bird) => {
    const timeline = gsap.timeline({
      repeat: -1,
      force3D: true,
      willChange: "transform"
    });

    const startX = bird.direction === 'right' ? '-10vw' : '110vw';
    const endX = bird.direction === 'right' ? '110vw' : '-10vw';

    // Animation de déplacement
    timeline.fromTo(birdElement,
      { x: startX },
      {
        x: endX,
        duration: 30 / bird.speed,
        ease: "none"
      }
    );

    // Animation de battement d'ailes (scale au lieu de rotation !)
    const wings = birdElement.querySelectorAll('[data-wing]');
    wings.forEach((wing) => {
      gsap.to(wing, {
        scaleY: 0.3,
        duration: 0.15,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });
    });

    // Animation de vol ondulant
    gsap.to(birdElement, {
      y: '+=15',
      duration: 2 + Math.random(),
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut"
    });

    return timeline;
  };

  // Mettre à jour l'affichage diurne
  const updateDiurnalDisplay = () => {
    if (!locationReady) return;

    const now = new Date();
    const newDiurnalOpacity = calculateDiurnalOpacity(now);
    
    console.log(`☀️ Éléments diurnes - Opacité: ${newDiurnalOpacity.toFixed(2)}`);
    setDiurnalOpacity(newDiurnalOpacity);
  };

  // Démarrer avec Paris par défaut
  useEffect(() => {
    setLocationReady(true);
  }, []);

  // Mise à jour dès que la géolocalisation est prête
  useEffect(() => {
    if (locationReady) {
      updateDiurnalDisplay();
    }
  }, [locationReady]);

  // Initialiser les éléments diurnes
  useEffect(() => {
    if (!containerRef.current) return;

    // Générer nuages et oiseaux
    cloudsRef.current = generateClouds();
    birdsRef.current = generateBirds();

    // Créer les éléments DOM pour les nuages
    cloudsRef.current.forEach((cloud) => {
      const cloudElement = document.createElement('div');
      cloudElement.className = 'absolute pointer-events-none';
      cloudElement.style.left = `${cloud.x}%`;
      cloudElement.style.top = `${cloud.y}%`;
      cloudElement.style.opacity = '0';
      cloudElement.setAttribute('data-cloud', 'true');
      cloudElement.style.willChange = 'transform, opacity';
      cloudElement.style.transform = 'translateZ(0)';
      
      // Forme du nuage avec CSS
      const size = cloud.size * 60; // Taille de base
      cloudElement.innerHTML = `
        <div style="
          width: ${size}px;
          height: ${size * 0.6}px;
          background: rgba(255, 255, 255, ${cloud.opacity});
          border-radius: 50px;
          position: relative;
          filter: blur(1px);
        ">
          <div style="
            position: absolute;
            top: -${size * 0.3}px;
            left: ${size * 0.2}px;
            width: ${size * 0.8}px;
            height: ${size * 0.8}px;
            background: rgba(255, 255, 255, ${cloud.opacity * 0.8});
            border-radius: 50%;
          "></div>
          <div style="
            position: absolute;
            top: -${size * 0.2}px;
            right: ${size * 0.1}px;
            width: ${size * 0.6}px;
            height: ${size * 0.6}px;
            background: rgba(255, 255, 255, ${cloud.opacity * 0.9});
            border-radius: 50%;
          "></div>
        </div>
      `;

      containerRef.current?.appendChild(cloudElement);

      // Créer l'animation
      const cloudAnimation = createCloudAnimation(cloudElement, cloud);
      animationsRef.current.push(cloudAnimation);
    });

    // Créer les éléments DOM pour les oiseaux
    birdsRef.current.forEach((bird) => {
      const birdElement = document.createElement('div');
      birdElement.className = 'absolute pointer-events-none';
      birdElement.style.left = `${bird.x}%`;
      birdElement.style.top = `${bird.y}%`;
      birdElement.style.opacity = '0';
      birdElement.setAttribute('data-bird', 'true');
      birdElement.style.willChange = 'transform, opacity';
      birdElement.style.transform = 'translateZ(0)';
      
      // Forme de l'oiseau (corps + ailes qui battent)
      birdElement.innerHTML = `
        <div style="
          width: 24px;
          height: 16px;
          position: relative;
          transform: ${bird.direction === 'left' ? 'scaleX(-1)' : 'scaleX(1)'};
        ">
          <!-- Corps de l'oiseau -->
          <div style="
            position: absolute;
            width: 6px;
            height: 3px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            top: 6px;
            left: 9px;
            z-index: 2;
          "></div>
          <!-- Aile gauche -->
          <div data-wing style="
            position: absolute;
            width: 10px;
            height: 2px;
            background: rgba(0, 0, 0, 0.6);
            transform: rotate(-25deg);
            transform-origin: right center;
            top: 5px;
            left: 2px;
            border-radius: 1px;
          "></div>
          <!-- Aile droite -->
          <div data-wing style="
            position: absolute;
            width: 10px;
            height: 2px;
            background: rgba(0, 0, 0, 0.6);
            transform: rotate(25deg);
            transform-origin: left center;
            top: 5px;
            right: 2px;
            border-radius: 1px;
          "></div>
        </div>
      `;

      containerRef.current?.appendChild(birdElement);

      // Créer l'animation
      const birdAnimation = createBirdAnimation(birdElement, bird);
      animationsRef.current.push(birdAnimation);
    });

    // Mise à jour initiale
    setTimeout(updateDiurnalDisplay, 100);

    // Mise à jour toutes les secondes
    const interval = setInterval(updateDiurnalDisplay, 1000);

    // Nettoyage
    return () => {
      clearInterval(interval);
      animationsRef.current.forEach(animation => animation.kill());
      animationsRef.current = [];
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, []);

  // Animer l'opacité des éléments diurnes
  useEffect(() => {
    if (!containerRef.current) return;

    const cloudElements = containerRef.current.querySelectorAll('[data-cloud]');
    const birdElements = containerRef.current.querySelectorAll('[data-bird]');

    [...cloudElements, ...birdElements].forEach((element) => {
      gsap.to(element, {
        opacity: diurnalOpacity,
        duration: 2,
        ease: "power2.out"
      });
    });

    // Gérer les animations selon la visibilité
    if (diurnalOpacity === 0) {
      animationsRef.current.forEach(animation => animation.pause());
    } else {
      animationsRef.current.forEach(animation => animation.resume());
    }
  }, [diurnalOpacity]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }} // Au-dessus des étoiles (z-index 0) mais sous le contenu
    />
  );
};

export default DiurnalLayer;
